// storage/caughtPokemonStorage.js
// Module for managing caught Pokemon in storage
// This is a wrapper around the PokemonManager for backward compatibility

import { logger } from '../utils/logger.js';
import { pokemonManager } from '../services/pokemon-manager.js';

/**
 * Load all caught Pokemon
 * @returns {Promise<Array>} - The caught Pokemon
 */
export async function loadCaughtPokemon() {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Return caught Pokemon (all Pokemon that are not in the team)
    return pokemonManager.getCaughtPokemon();
  } catch (e) {
    logger.error('Error loading caught Pokemon:', e);
    return [];
  }
}

/**
 * Save the caught Pokemon list
 * @param {Array} caughtPokemon - The caught Pokemon to save
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function saveCaughtPokemon(caughtPokemon) {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Update all Pokemon in the caught list
    for (const pokemon of caughtPokemon) {
      await pokemonManager.updatePokemon(pokemon);
    }

    // Save all Pokemon
    return await pokemonManager.saveAllPokemon();
  } catch (e) {
    logger.error('Error saving caught Pokemon:', e);
    return false;
  }
}

/**
 * Add a Pokemon to the caught list
 * @param {Object} pokemon - The Pokemon to add
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function addCaughtPokemon(pokemon) {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Add caught timestamp if not already set
    if (!pokemon.caughtAt) {
      pokemon.caughtAt = Date.now();
    }

    // Add to Pokemon manager
    return await pokemonManager.addPokemon(pokemon);
  } catch (e) {
    logger.error('Error adding caught Pokemon:', e);
    return false;
  }
}

/**
 * Remove a Pokemon from the caught list
 * @param {string} id - The ID of the Pokemon to remove
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function removeCaughtPokemon(id) {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Remove from Pokemon manager
    return await pokemonManager.removePokemon(id);
  } catch (e) {
    logger.error('Error removing caught Pokemon:', e);
    return false;
  }
}

/**
 * Update a Pokemon in the caught list
 * @param {Object} pokemon - The Pokemon to update
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function updateCaughtPokemon(pokemon) {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Update in Pokemon manager
    const success = await pokemonManager.updatePokemon(pokemon);

    if (success) {
      logger.debug(`Updated Pokemon ${pokemon.name} (ID: ${pokemon.id}) with level ${pokemon.level} and XP ${pokemon.experience}`);
    }

    return success;
  } catch (e) {
    logger.error('Error updating caught Pokemon:', e);
    return false;
  }
}

/**
 * Remove a Pokemon from the caught list
 * @param {string} pokemonId - The ID of the Pokemon to remove
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function removeCaughtPokemon(pokemonId) {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Remove from Pokemon manager
    const success = await pokemonManager.removePokemon(pokemonId);

    if (success) {
      logger.debug(`Removed Pokemon with ID ${pokemonId} from caught Pokemon storage`);
    }

    return success;
  } catch (e) {
    logger.error('Error removing caught Pokemon:', e);
    return false;
  }
}

/**
 * Check if a Pokemon is in the caught list
 * @param {string} id - The ID of the Pokemon to check
 * @returns {Promise<boolean>} - Whether the Pokemon is in the caught list
 */
export async function isPokemonCaught(id) {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Check if Pokemon exists in the manager
    const pokemon = pokemonManager.getPokemonById(id);

    // It's caught if it exists and is not in the team
    return pokemon !== null && !pokemonManager.teamIds.has(id);
  } catch (e) {
    logger.error('Error checking if Pokemon is caught:', e);
    return false;
  }
}
